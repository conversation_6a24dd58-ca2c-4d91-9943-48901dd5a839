#!/usr/bin/env python3
"""
Test script to verify the direct search functionality works correctly.
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.open_webui.utils.middleware import is_realtime_query

def test_realtime_query_detection():
    """Test the real-time query detection function."""
    
    # Test cases that should be detected as real-time
    realtime_queries = [
        "what is the current date today",
        "current weather in New York",
        "latest news about AI",
        "stock price of Apple right now",
        "what time is it now",
        "today's temperature",
        "recent developments in technology",
        "breaking news",
        "this week's events",
        "what's happening now"
    ]
    
    # Test cases that should NOT be detected as real-time
    non_realtime_queries = [
        "explain quantum physics",
        "history of World War II",
        "how to cook pasta",
        "Python programming tutorial",
        "mathematical concepts",
        "literature analysis",
        "philosophical questions",
        "scientific theories"
    ]
    
    print("Testing real-time query detection...")
    
    # Test real-time queries
    for query in realtime_queries:
        result = is_realtime_query(query)
        status = "✓" if result else "✗"
        print(f"{status} '{query}' -> {result}")
        if not result:
            print(f"  ERROR: Should be detected as real-time!")
    
    print("\nTesting non-real-time queries...")
    
    # Test non-real-time queries
    for query in non_realtime_queries:
        result = is_realtime_query(query)
        status = "✓" if not result else "✗"
        print(f"{status} '{query}' -> {result}")
        if result:
            print(f"  ERROR: Should NOT be detected as real-time!")
    
    print("\nReal-time query detection test completed!")

if __name__ == "__main__":
    test_realtime_query_detection()
